import { createHash } from 'crypto'
import { getPdfFileClassification } from '@/lib/pdf/_helpers/getPdfFileClassification'
import { tasksFlow } from '@/lib/pdf/_helpers/tasksFlow'
import { taxesFlow } from '@/lib/pdf/_helpers/taxesFlow'
import { ocr } from '@/lib/pdf/ocr'
import { BaseTable } from '../baseTable'
import type { db } from '../db'
import { ClassificationsTable } from './classifications.table'
import { DocumentsFilesTable } from './documentsFiles.table'

const IS_TRIGGER_DEV = process.env.IS_TRIGGER_DEV // this variable is set to "true" only in the Trigger.dev environment

export class FilesTable extends BaseTable {
	readonly table = 'files'

	columns = this.setColumns(t => ({
		customerId: t.uuid().foreignKey('customers', 'id', { name: 'files_customer_id_customers_id_fk' }).nullable(),
		created: t.timestampNoTZ().default(t.sql`now()`),
		updated: t.timestampNoTZ().default(t.sql`now()`),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		name: t.text(),
		type: t.enum('public.files_type_enum', ['xml', 'pdf', 'docx', 'xlsx', 'png', 'pptx']),
		userId: t.uuid().foreignKey('users', 'id', { name: 'files_user_id_users_id_fk', onUpdate: 'CASCADE', onDelete: 'SET NULL' }).nullable(),
		classificationId: t
			.uuid()
			.foreignKey('classifications', 'id', {
				name: 'files_classification_id_classifications_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'SET NULL',
			})
			.nullable(),
		hash: t.type('bpchar').unique('files_hash_index').nullable(),
		packId: t.uuid().nullable(),
		data: t.text().select(false), // this is a virtual column, it does not exists in the database
	}))

	init(orm: typeof db) {
		this.afterSave([], async () => {
			if (IS_TRIGGER_DEV) return // avoid executing the function revalidateTag in Trigger.dev environment, because it leads to an error
			const { revalidateTag } = await import('next/cache')
			revalidateTag(this.table)
			revalidateTag(orm.filesTasksFlow.table)
			revalidateTag(orm.filesTaxesFlow.table)
		})

		// save file in Azure Blob Storage and create a hash
		this.beforeSave(async ({ q }) => {
			if (!('columns' in q) || !('values' in q) || !Array.isArray(q.values)) return

			if (!q.columns.includes('id')) q.columns.push('id')
			if (!q.columns.includes('hash')) q.columns.push('hash')

			const dataColumn = q.columns.findIndex(e => e === 'data')
			const typeColumn = q.columns.findIndex(e => e === 'type')
			const idColumn = q.columns.findIndex(e => e === 'id')
			const hashColumn = q.columns.findIndex(e => e === 'hash')

			const { put } = await import('@/helpers/storage/put')

			for (const row of q.values) {
				if (!Array.isArray(row)) continue

				const data = row[dataColumn]
				const type = row[typeColumn]
				const id = row[idColumn] ?? crypto.randomUUID()

				if (typeof data !== 'string' || typeof type !== 'string' || typeof id !== 'string') continue

				// Calculate hash and intercept query to include the hash in the update
				const hash = createHash('sha256').update(data).digest('hex')

				// upload data to Azure Blob Storage
				const [uploadedData, uploadedError] = await put({ blobName: `${id}.${type}`, containerName: 'files', data })
				if (!uploadedData) throw uploadedError

				row[hashColumn] = hash
				row[idColumn] = id

				// remove data from the row
				row.splice(dataColumn, 1)
			}

			// remove data from the query
			q.columns = q.columns.filter(e => e !== 'data')
		})

		// process file after save
		this.afterSave(['id', 'type', 'classificationId'], async rows => {
			const { get } = await import('@/helpers/storage/get')

			for (const { id, type, classificationId: oldClassificationId } of rows) {
				if (type !== 'pdf') continue

				const [data, error] = await get({ blobName: `${id}.${type}`, containerName: 'files' })
				if (!data) throw error

				const [ocrResult, ocrResultError] = await ocr({ dataBase64: data })
				if (!ocrResult) throw ocrResultError

				const [fileClassification] = await getPdfFileClassification({ ocrResult })
				if (!fileClassification?.classificationId) continue
				const classificationId = fileClassification.classificationId

				if (classificationId !== oldClassificationId) {
					await orm.files.where({ id }).update({ classificationId })
				}

				const taskFlowId = await orm.classificationsFlowsTasks.getOptional('id').where({ classificationId })
				const taxesFlowId = await orm.classificationsFlowsTaxes.getOptional('id').where({ classificationId })

				if (!taskFlowId && !taxesFlowId) continue

				const text = ocrResult.map(page => page.join('\n')).join(' ')
				taskFlowId ? await tasksFlow({ fileId: id, classificationId, text }) : await orm.filesTasksFlow.where({ fileId: id }).delete()
				taxesFlowId ? await taxesFlow({ fileId: id, classificationId, text }) : await orm.filesTaxesFlow.where({ fileId: id }).delete()
			}
		})

		this.afterDelete(['id', 'type'], async rows => {
			const { del } = await import('@/helpers/storage/del')

			for (const { id, type } of rows) {
				await del({ blobName: `${id}.${type}`, containerName: 'files' })
			}
		})
	}

	computed = this.setComputed(q => ({
		fileData: q.computeBatchAtRuntime(
			['id', 'type'], // columns it depends on
			async files => {
				const { get } = await import('@/helpers/storage/get')

				const fileData = await Promise.all(
					files.map(async ({ id, type }) => {
						const [data, error] = await get({ blobName: `${id}.${type}`, containerName: 'files' })
						if (!data) throw error

						return data
					}),
				)

				return fileData
			},
		),
	}))

	relations = {
		classification: this.hasOne(() => ClassificationsTable, { columns: ['classificationId'], references: ['id'] }),

		documentsFiles: this.hasOne(() => DocumentsFilesTable, { columns: ['id'], references: ['fileId'] }),
	}
}
