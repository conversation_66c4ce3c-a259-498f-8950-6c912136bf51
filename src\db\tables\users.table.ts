import type { db } from '@/src/db/db'
import { isCPF } from 'brazilian-values'
import { z } from 'zod'
import { BaseTable, sql } from '../baseTable'
import { ClerkUsersTable } from './clerkUsers.table'
import { CustomersTable } from './customers.table'
import { PermissionsTable } from './permissions.table'

export class UsersTable extends BaseTable {
	readonly table = 'users'

	columns = this.setColumns(t => ({
		cpf: t.text().input(s => s.transform(v => v.replace(/[^\d]/g, '')).refine(isCPF, 'CPF inválido')),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		clerkId: t.varchar(32).unique(),
		role: t.text().nullable(),
		selectedCustomerId: t
			.uuid()
			.foreignKey('customers', 'id', {
				name: 'users_selected_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			})
			.nullable(),
		selectedFromDate: t.timestampNoTZ().default(sql`now()`),
		selectedUntilDate: t.timestampNoTZ().default(sql`now()`),
		tags: t.json(z.array(z.string())),
	}))

	relations = {
		permissions: this.hasMany(() => PermissionsTable, { columns: ['id'], references: ['userId'] }),
		customers: this.hasMany(() => CustomersTable, { through: 'permissions', source: 'customer' }),
		clerkUsers: this.hasMany(() => ClerkUsersTable, { columns: ['clerkId'], references: ['clerkId'] }),
	}

	init(orm: typeof db) {
		this.afterSaveCommit(['id'], async () => {
			const { revalidateTag } = await import('next/cache')
			revalidateTag(orm.users.table)
			revalidateTag(orm.permissions.table)
		})
	}
}
