import { BaseTable } from '../baseTable'
import type { db } from '../db'
import { TasksClassificationsTable } from './tasksClassifications.table'
import { TasksRolesTable } from './tasksRoles.table'

export class TasksTable extends BaseTable {
	readonly table = 'tasks'

	columns = this.setColumns(t => ({
		dateEnd: t.timestampNoTZ().nullable(),
		dateStart: t.timestampNoTZ(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		title: t.text(),
		applyToRules: t.json().default(t.sql`'{}'::jsonb`),
	}))

	relations = {
		classifications: this.hasMany(() => TasksClassificationsTable, { columns: ['id'], references: ['taskId'] }),
		roles: this.hasMany(() => TasksRolesTable, { columns: ['id'], references: ['taskId'] }),
	}

	init(orm: typeof db) {
		this.afterSave([], async () => {
			const { revalidateTag } = await import('next/cache')
			revalidateTag(orm.files.table)
			revalidateTag(orm.filesTasksFlow.table)
			revalidateTag(orm.filesTaxesFlow.table)
		})
	}
}
